/**
 * Presence Hook for HypeArrow
 * 
 * Handles anonymous user identification and presence tracking cleanup.
 * Manages localStorage for persistent anonymous user IDs across page refreshes.
 */

const ANONYMOUS_ID_KEY = 'hype_arrow_anonymous_id';

/**
 * Generate a unique anonymous identifier
 * @returns {string} A unique anonymous ID
 */
function generateAnonymousId() {
  const randomBytes = new Uint8Array(8);
  crypto.getRandomValues(randomBytes);
  const randomHex = Array.from(randomBytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
  return `anon_${randomHex}`;
}

/**
 * Get or create an anonymous ID from localStorage
 * @returns {string} The anonymous ID
 */
function getOrCreateAnonymousId() {
  let anonymousId = localStorage.getItem(ANONYMOUS_ID_KEY);
  
  if (!anonymousId) {
    anonymousId = generateAnonymousId();
    localStorage.setItem(ANONYMOUS_ID_KEY, anonymousId);
  }
  
  return anonymousId;
}

/**
 * Clear the anonymous ID from localStorage
 * Useful for testing or when user logs in
 */
function clearAnonymousId() {
  localStorage.removeItem(ANONYMOUS_ID_KEY);
}

const PresenceHook = {
  mounted() {
    console.log('Presence hook mounted');
    
    // Get or create anonymous ID for non-authenticated users
    const anonymousId = getOrCreateAnonymousId();
    console.log('Anonymous ID:', anonymousId);
    
    // Send the anonymous ID to the LiveView
    this.pushEvent('set_anonymous_id', { anonymous_id: anonymousId });
    
    // Handle page visibility changes to manage presence
    this.handleVisibilityChange = () => {
      if (document.hidden) {
        // Page is hidden, user might be navigating away
        console.log('Page hidden');
        this.pushEvent('page_hidden', {});
      } else {
        // Page is visible again
        console.log('Page visible');
        this.pushEvent('page_visible', {});
      }
    };
    
    // Handle beforeunload to clean up presence
    this.handleBeforeUnload = () => {
      // Send a synchronous request to clean up presence
      console.log('User leaving');
      this.pushEvent('user_leaving', {});
    };
    
    // Add event listeners
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    window.addEventListener('beforeunload', this.handleBeforeUnload);
    
    // Store references for cleanup
    this.visibilityHandler = this.handleVisibilityChange;
    this.beforeUnloadHandler = this.handleBeforeUnload;
  },
  
  destroyed() {
    console.log('Presence hook destroyed');
    
    // Clean up event listeners
    if (this.visibilityHandler) {
      document.removeEventListener('visibilitychange', this.visibilityHandler);
    }
    
    if (this.beforeUnloadHandler) {
      window.removeEventListener('beforeunload', this.beforeUnloadHandler);
    }
    
    // Notify the server that the user is leaving
    this.pushEvent('user_leaving', {});
  },
  
  // Handle events from the server
  handleEvent(event, payload) {
    switch (event) {
      case 'presence_updated':
        console.log('Presence updated:', payload);
        // Could trigger UI updates here if needed
        break;
        
      case 'clear_anonymous_id':
        // Clear anonymous ID when user logs in
        clearAnonymousId();
        break;
        
      default:
        console.log('Unknown presence event:', event, payload);
    }
  }
};

// Export for use in other modules if needed
export { getOrCreateAnonymousId, clearAnonymousId };

export default PresenceHook;
