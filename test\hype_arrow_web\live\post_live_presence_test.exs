defmodule HypeArrowWeb.PostLivePresenceTest do
  use HypeArrowWeb.ConnCase

  import Phoenix.LiveViewTest
  import HypeArrow.SocialFixtures
  import HypeArrow.AccountsFixtures

  alias HypeArrow.Presence

  describe "presence tracking" do
    setup do
      user = user_fixture()
      post = post_fixture(user_id: user.id)
      %{user: user, post: post}
    end

    test "tracks presence when viewing a post", %{conn: conn, user: user, post: post} do
      conn = log_in_user(conn, user)
      
      # Navigate to the post
      {:ok, view, _html} = live(conn, ~p"/posts/#{post.id}")
      
      # Wait a moment for presence to be tracked
      :timer.sleep(100)
      
      # Check that presence is tracked
      viewers = Presence.list_post_viewers(post.id)
      assert length(viewers) == 1
      
      # Navigate away from the post
      {:ok, _view, _html} = live(conn, ~p"/")
      
      # Wait a moment for presence to be cleaned up
      :timer.sleep(100)
      
      # Check that presence is cleaned up
      viewers = Presence.list_post_viewers(post.id)
      assert length(viewers) == 0
    end

    test "cleans up presence when collapsing a post", %{conn: conn, user: user, post: post} do
      conn = log_in_user(conn, user)
      
      # Start at the main page
      {:ok, view, _html} = live(conn, ~p"/")
      
      # Expand the post by clicking on it
      view |> element("[data-post-id='#{post.id}']") |> render_click()
      
      # Wait a moment for presence to be tracked
      :timer.sleep(100)
      
      # Check that presence is tracked
      viewers = Presence.list_post_viewers(post.id)
      assert length(viewers) == 1
      
      # Collapse the post by clicking on it again
      view |> element("[data-post-id='#{post.id}']") |> render_click()
      
      # Wait a moment for presence to be cleaned up
      :timer.sleep(100)
      
      # Check that presence is cleaned up
      viewers = Presence.list_post_viewers(post.id)
      assert length(viewers) == 0
    end

    test "switches presence tracking when navigating between posts", %{conn: conn, user: user} do
      conn = log_in_user(conn, user)
      
      # Create two posts
      post1 = post_fixture(user_id: user.id)
      post2 = post_fixture(user_id: user.id)
      
      # Navigate to first post
      {:ok, view, _html} = live(conn, ~p"/posts/#{post1.id}")
      
      # Wait a moment for presence to be tracked
      :timer.sleep(100)
      
      # Check that presence is tracked for post1
      viewers1 = Presence.list_post_viewers(post1.id)
      viewers2 = Presence.list_post_viewers(post2.id)
      assert length(viewers1) == 1
      assert length(viewers2) == 0
      
      # Navigate to second post
      {:ok, _view, _html} = live(conn, ~p"/posts/#{post2.id}")
      
      # Wait a moment for presence to be updated
      :timer.sleep(100)
      
      # Check that presence is now tracked for post2 and cleaned up for post1
      viewers1 = Presence.list_post_viewers(post1.id)
      viewers2 = Presence.list_post_viewers(post2.id)
      assert length(viewers1) == 0
      assert length(viewers2) == 1
    end
  end
end
